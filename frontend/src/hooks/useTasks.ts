import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getTasks, 
  getTaskById, 
  createTask, 
  updateTask, 
  updateTaskStatus, 
  deleteTask,
  Task,
  CreateTaskData
} from '@/services/task.service';
import { taskKeys } from '@/lib/cache/taskCache';
import { useAtom } from 'jotai';
import { organizationsAtom } from '@/atoms/organizations';

interface UseTasksOptions {
  orgId?: string;
  filters?: {
    status?: string;
  };
}

export function useTasks({ orgId, filters }: UseTasksOptions = {}) {
  const queryClient = useQueryClient();
  const [organizations] = useAtom(organizationsAtom);

  // Fetch tasks query
  const { data: tasks, isLoading, error } = useQuery({
    queryKey: orgId ? taskKeys.list({ orgId, ...filters }) : taskKeys.all,
    queryFn: () => getTasks(orgId, filters),
    staleTime: 30 * 1000,
    refetchOnMount: true,
    refetchOnWindowFocus: true
  });

  // Prefetch tasks for all organizations
  const prefetchAllTasks = async () => {
    if (!orgId && organizations.length > 0) {
      const taskPromises = organizations.map(org => 
        queryClient.prefetchQuery({
          queryKey: taskKeys.list({ orgId: org.id }),
          queryFn: () => getTasks(org.id),
          staleTime: 30 * 1000,
        })
      );
      await Promise.all(taskPromises);
    }
  };

  // Create task mutation
  const createTaskMutation = useMutation({
    mutationFn: (newTask: CreateTaskData) => createTask(newTask),
    onSuccess: () => {
      // Invalidate tasks list query
      queryClient.invalidateQueries({
        queryKey: taskKeys.lists(),
      });
    },
  });

  // Update task mutation
  const updateTaskMutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: Partial<CreateTaskData> }) =>
      updateTask(taskId, data),
    onSuccess: (updatedTask) => {
      // Update task in cache
      queryClient.setQueryData(
        taskKeys.detail(updatedTask.id),
        updatedTask
      );
      // Invalidate tasks list
      queryClient.invalidateQueries({
        queryKey: taskKeys.lists(),
      });
    },
  });

  // Update task status mutation
  const updateTaskStatusMutation = useMutation({
    mutationFn: ({ taskId, status }: { taskId: string; status: "pending" | "in_progress" | "completed" }) =>
      updateTaskStatus(taskId, status),
    onSuccess: (updatedTask) => {
      // Update task in cache
      queryClient.setQueryData(
        taskKeys.detail(updatedTask.id),
        updatedTask
      );
      // Invalidate tasks list
      queryClient.invalidateQueries({
        queryKey: taskKeys.lists(),
      });
    },
  });

  // Delete task mutation
  const deleteTaskMutation = useMutation({
    mutationFn: deleteTask,
    onSuccess: (_, taskId) => {
      // Remove task from cache
      queryClient.removeQueries({
        queryKey: taskKeys.detail(taskId),
      });
      // Invalidate tasks list
      queryClient.invalidateQueries({
        queryKey: taskKeys.lists(),
      });
    },
  });

  return {
    tasks,
    isLoading,
    error,
    createTask: createTaskMutation.mutate,
    updateTask: updateTaskMutation.mutate,
    updateTaskStatus: updateTaskStatusMutation.mutate,
    deleteTask: deleteTaskMutation.mutate,
    isCreating: createTaskMutation.isPending,
    isUpdating: updateTaskMutation.isPending,
    isUpdatingStatus: updateTaskStatusMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    prefetchAllTasks,
  };
} 