import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/components/hooks/use-toast";
import { deleteOrganization } from "@/services/organization.service";
import { removeMember, leaveOrganization } from "@/services/membership.service";
import { useAuth } from "@/hooks/useAuth";

import { Copy, ChevronDown, ArrowLeft, X, Trash2 } from "lucide-react";
import Modal from "@/components/ui/Modal";

import { useOrganization } from "@/hooks/useOrganizations";

interface TaskCardProps {
  task: any;
}

const TaskCard = ({ task }: TaskCardProps) => (
  <div className="bg-[#303030] p-4 rounded-lg">
    <div className="flex justify-between items-start">
      <div>
        <h4 className="text-gray-100 font-medium">{task.title}</h4>
        <p className="text-gray-400 text-sm mt-1">{task.description}</p>
      </div>
      <span
        className={`px-2 py-1 text-xs rounded ${
          task.status === "completed"
            ? "bg-green-500 text-white"
            : "bg-yellow-500 text-black"
        }`}
      >
        {task.status}
      </span>
    </div>
    <div className="mt-3 text-sm text-gray-400">
      <p>Assigned to: {task.assignedTo?.fullName || "Unassigned"}</p>
      <p>
        Due:{" "}
        {task.dueDate
          ? new Date(task.dueDate).toLocaleDateString()
          : "No due date"}
      </p>
    </div>
  </div>
);

export default function OrganizationDetails() {
  const { orgId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [showTasks, setShowTasks] = useState(true);
  const [showMembers, setShowMembers] = useState(true);

  const { organization, isLoading, error } = useOrganization(orgId);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !organization) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error</h2>
        <p className="text-gray-400">Failed to load organization details.</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate("/dashboard/organizations")}
        >
          Go Back
        </Button>
      </div>
    );
  }

  // Check if current user is the owner
  const isOwner = organization.createdBy === user?.id;

  // Check if user is a member
  const isMember =
    organization.members?.some((member) => member.id === user?.id) || isOwner;

  const handleLeaveOrganization = async () => {
    if (isOwner || !orgId || !isMember) return;

    try {
      await leaveOrganization(orgId);
      navigate("/organizations");

      toast({
        title: "Success",
        description: "You have left the organization",
        type: "success",
      });
    } catch (error) {
      console.error("Failed to leave organization:", error);
      toast({
        title: "Error",
        description: "Failed to leave organization",
        type: "error",
      });
    }
  };

  const handleDeleteOrganization = async () => {
    if (!isOwner || !orgId) return;

    try {
      await deleteOrganization(orgId);
      navigate("/organizations");

      toast({
        title: "Success",
        description: "Organization deleted successfully",
        type: "success",
      });
    } catch (error) {
      console.error("Failed to delete organization:", error);
      toast({
        title: "Error",
        description: "Failed to delete organization",
        type: "error",
      });
    }
  };

  const copyInviteCode = async () => {
    try {
      await navigator.clipboard.writeText(organization?.inviteCode || "");
      toast({
        title: "Success",
        description: "Invite code copied to clipboard",
        type: "success",
      });
    } catch (error) {
      console.error("Error copying invite code:", error);
      toast({
        title: "Error",
        description: "Failed to copy invite code",
        type: "error",
      });
    }
  };

  const handleDeleteMember = async () => {
    if (!selectedMemberId || !orgId || !isOwner) return;

    try {
      await removeMember(orgId, selectedMemberId);

      // Update local state
      // Note: The useOrganization hook should handle this automatically

      toast({
        title: "Success",
        description: "Member removed from organization",
        type: "success",
      });
    } catch (error) {
      console.error("Failed to remove member:", error);
      toast({
        title: "Error",
        description: "Failed to remove member",
        type: "error",
      });
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate("/dashboard/organizations")}
          className="mr-4 bg-white cursor-pointer"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold text-gray-100">
          {organization.name}
        </h1>
      </div>

      <div className="mb-6">
        <h2 className="text-gray-300">Detail:</h2>
        <p className="text-gray-400">
          {organization.description || "No description provided."}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Members Card */}
        <div
          className={`bg-[#404040] rounded-lg ${
            !showMembers ? "col-span-3" : ""
          }`}
        >
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-400">Total Members</p>
                <p className="text-2xl font-bold text-gray-100">
                  {organization._count?.members ||
                    organization.members?.length ||
                    0}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMembers(!showMembers)}
              >
                {showMembers ? (
                  <X className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>

          {showMembers && (
            <div className="px-6 pb-6">
              <div className="space-y-4">
                {organization.members?.map((member: any) => (
                  <div
                    key={member.id}
                    className="bg-[#303030] p-4 rounded-lg flex items-center justify-between"
                  >
                    <div>
                      <p className="text-gray-100 font-medium">
                        {member.fullName}
                      </p>
                      <p className="text-gray-400 text-sm">{member.email}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                      {member.isOwner ? (
                        <span className="bg-blue-500 text-white px-2 py-1 rounded text-sm">
                          Owner
                        </span>
                      ) : (
                        <>
                          <span className="bg-gray-600 text-white px-2 py-1 rounded text-sm">
                            Member
                          </span>
                          {isOwner && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setSelectedMemberId(member.id)}
                              className="text-red-500 hover:text-red-400 hover:bg-red-500/10 cursor-pointer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* All Tasks Card */}
        <div
          className={`bg-[#404040] rounded-lg ${
            !showTasks ? "col-span-3" : ""
          }`}
        >
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-400">Total Tasks</p>
                <p className="text-2xl font-bold text-gray-100">
                  {organization.stats?.totalTasks || 0}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTasks(!showTasks)}
              >
                {showTasks ? (
                  <X className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>

          {showTasks && (
            <div className="px-6 pb-6">
              <div className="space-y-4">
                {organization.tasks?.map((task: any) => (
                  <TaskCard key={task.id} task={task} />
                ))}
                {(!organization.tasks || organization.tasks.length === 0) && (
                  <p className="text-gray-400 text-center py-4">
                    No tasks found
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Pending Tasks Card */}
        <div
          className={`bg-[#404040] rounded-lg ${
            !showTasks ? "col-span-3" : ""
          }`}
        >
          <div className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-400">Pending Tasks</p>
                <p className="text-2xl font-bold text-gray-100">
                  {organization.stats?.pendingTasks || 0}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTasks(!showTasks)}
              >
                {showTasks ? (
                  <X className="h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>

          {showTasks && (
            <div className="px-6 pb-6">
              <div className="space-y-4">
                {organization.tasks
                  ?.filter((task: any) => task.status !== "completed")
                  .map((task: any) => (
                    <TaskCard key={task.id} task={task} />
                  ))}
                {(!organization.tasks ||
                  organization.tasks.filter(
                    (t: any) => t.status !== "completed"
                  ).length === 0) && (
                  <p className="text-gray-400 text-center py-4">
                    No pending tasks
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Invite Code Section */}
      {organization.isOwner && (
        <div className="bg-[#303030] p-6 rounded-lg mb-8">
          <h3 className="text-xl font-semibold text-gray-100 mb-4">
            Invite Code
          </h3>
          <div className="flex items-center space-x-2">
            <code className="bg-[#404040] px-3 py-1 rounded text-white flex-1">
              {organization.inviteCode}
            </code>
            <Button
              variant="outline"
              size="sm"
              className="cursor-pointer"
              onClick={copyInviteCode}
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-end space-x-4">
        {!isOwner && (
          <Button
            onClick={handleLeaveOrganization}
            className="bg-red-600 hover:bg-red-700 text-white cursor-pointer"
          >
            Leave Organization
          </Button>
        )}

        {isOwner && (
          <Button
            onClick={() => setShowDeleteModal(true)}
            className="bg-red-600 hover:bg-red-700 text-white cursor-pointer"
          >
            Delete Organization
          </Button>
        )}
      </div>

      {/* Delete Member Confirmation Modal */}
      <Modal
        isOpen={!!selectedMemberId}
        onClose={() => setSelectedMemberId(null)}
        title="Remove Member"
      >
        <div className="p-6 text-center">
          <h3 className="text-lg font-medium text-white mb-4">
            Remove Member?
          </h3>
          <p className="text-gray-400 mb-6">
            This action cannot be undone. The member will lose access to all
            organization resources.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline" onClick={() => setSelectedMemberId(null)}>
              Cancel
            </Button>
            <Button
              onClick={handleDeleteMember}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Remove Member
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Organization"
      >
        <div className="p-6 text-center">
          <h3 className="text-lg font-medium text-white mb-4">
            Are you sure you want to delete this organization?
          </h3>
          <p className="text-gray-400 mb-6">
            This action cannot be undone. All tasks and data will be permanently
            deleted.
          </p>
          <div className="flex justify-center space-x-4">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleDeleteOrganization}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete Organization
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
