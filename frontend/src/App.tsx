//frontend/src/App.tsx
import { Toaster } from "sonner";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { useEffect, useState } from "react";
import { useAtom } from "jotai";
import { authAtom } from "./atoms/pageAtom";
import { useAuth } from "./hooks/useAuth";
import Loader from "./components/loaders/loader";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import DashboardLayout from "./pages/dashboard/Layout";
import { AuthProvider } from "./context/AuthContext";
import Summary from "./components/dashboard/Summary";
import Tasks from "./components/dashboard/Tasks";
import OrganizationRoutes from "@/routes/organization.routes";

// Auth Guard for public routes (login/signup)
function PublicRoute({ children }: { children: React.ReactNode }) {
  const [auth] = useAtom(authAtom);
  const location = useLocation();

  if (auth.isAuthenticated) {
    return <Navigate to="/dashboard" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

// Auth Guard for protected routes
function PrivateRoute({ children }: { children: React.ReactNode }) {
  const [auth] = useAtom(authAtom);
  const location = useLocation();

  if (!auth.isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

const App = () => {
  const [isHydrated, setIsHydrated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { checkAuth } = useAuth();

  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        await checkAuth();
      } finally {
        setIsHydrated(true);
        setIsLoading(false);
      }
    };
    initAuth();
  }, [checkAuth]);

  if (!isHydrated || isLoading) {
    return <Loader />;
  }

  return (
    <Router>
      <AuthProvider>
        <div className="bg-[#09090B] min-h-screen">
          <Toaster position="top-center" />
          <Routes>
            {/* Public Routes */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              }
            />
            <Route
              path="/signup"
              element={
                <PublicRoute>
                  <Signup />
                </PublicRoute>
              }
            />

            {/* Protected Dashboard Routes */}
            <Route
              path="/dashboard"
              element={
                <PrivateRoute>
                  <DashboardLayout />
                </PrivateRoute>
              }
            >
              {/* Add redirect for /dashboard */}
              <Route
                index
                element={<Navigate to="/dashboard/home" replace />}
              />
              {/* Remove path="home" from index route */}
              <Route path="home" element={<Summary />} />
              {/* Organization Routes */}
              <Route path="organizations/*" element={<OrganizationRoutes />} />
              <Route path="tasks" element={<Tasks />} />
            </Route>

            {/* Default redirect */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </AuthProvider>
    </Router>
  );
};

export default App;
