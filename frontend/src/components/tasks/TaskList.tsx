// components/tasks/TaskList.tsx

import { useState, useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import TaskDetails from "@/components/tasks/TaskDetails";
import TaskForm from "../forms/TaskForm";
import { Task } from "@/services/task.service";
import { useAtom } from "jotai";
import { organizationsAtom } from "@/atoms/organizations";
import { userAtom } from "@/atoms/user";
import { useTasks } from "@/hooks/useTasks";

interface TaskListProps {
  orgId?: string; // Optional - if provided, only shows tasks for that organization
}

export default function TaskList({ orgId }: TaskListProps) {
  const [selectedTask, setSelectedTask] = useState<string | null>(null);
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editTask, setEditTask] = useState<Task | null>(null);
  const [organizations] = useAtom(organizationsAtom);
  const [currentUser] = useAtom(userAtom);

  // Filtering and sorting
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [searchTerm, setSearchTerm] = useState("");

  // Check if user is owner of the current organization
  const currentOrg = organizations.find((org) => org.id === orgId);
  const isOwner = currentOrg?.isOwner ?? false;

  // Use the tasks hook with status filter
  const {
    tasks = [],
    isLoading,
    createTask,
    updateTask,
    updateTaskStatus,
    deleteTask,
    isCreating,
    isUpdating,
    isUpdatingStatus,
    isDeleting,
    prefetchAllTasks,
  } = useTasks({
    orgId,
    filters: {
      status: statusFilter === "all" ? undefined : statusFilter,
    },
  });

  // Prefetch all tasks when component mounts
  useEffect(() => {
    if (!orgId) {
      prefetchAllTasks();
    }
  }, [orgId]);

  const handleCreateTask = () => {
    setEditTask(null);
    setShowTaskForm(true);
  };

  const handleEditTask = (task: Task) => {
    setEditTask(task);
    setShowTaskForm(true);
  };

  const handleTaskFormSubmit = () => {
    setShowTaskForm(false);
  };

  const handleTaskFormCancel = () => {
    setShowTaskForm(false);
    setEditTask(null);
  };

  // Remove client-side filtering since we're using backend filtering
  const filteredAndSortedTasks = tasks
    .filter((task) => {
      // Only filter by search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        return (
          task.title.toLowerCase().includes(term) ||
          (task.description && task.description.toLowerCase().includes(term))
        );
      }
      return true;
    })
    .sort((a, b) => {
      // Handle sorting by different fields
      let compareA: any = a[sortField as keyof Task];
      let compareB: any = b[sortField as keyof Task];

      // Handle nested fields
      if (sortField === "organization") compareA = a.organization?.name;
      if (sortField === "organization") compareB = b.organization?.name;
      if (sortField === "assignee") compareA = a.assignee?.fullName;
      if (sortField === "assignee") compareB = b.assignee?.fullName;

      // Handle null values
      if (compareA === null || compareA === undefined)
        return sortDirection === "asc" ? -1 : 1;
      if (compareB === null || compareB === undefined)
        return sortDirection === "asc" ? 1 : -1;

      // Sort strings or dates
      if (typeof compareA === "string") {
        const comparison = compareA.localeCompare(compareB);
        return sortDirection === "asc" ? comparison : -comparison;
      }

      // Sort numbers
      return sortDirection === "asc"
        ? compareA - compareB
        : compareB - compareA;
    });

  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-900 text-yellow-300";
      case "in-progress":
        return "bg-blue-900 text-blue-300";
      case "completed":
        return "bg-green-900 text-green-300";
      default:
        return "bg-gray-700 text-gray-300";
    }
  };

  // Check if user can edit a task
  const canEditTask = (task: Task) => {
    // User can edit if they created the task or if they're the org owner
    const isTaskCreator = task.createdBy === currentUser?.id;
    const isOrgOwner = task.organization?.ownerId === currentUser?.id;
    return isTaskCreator || isOrgOwner;
  };

  const transformTaskForForm = (task: Task | null) => {
    if (!task) return undefined;
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      status: task.status as "pending" | "in_progress" | "completed",
      dueAt: task.dueAt,
      assignedTo: task.assignee?.id,
      orgId: task.organization?.id,
    };
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-10">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">
            {orgId ? "Organization Tasks" : "My Tasks"}
          </h2>
          {(isOwner || !orgId) && (
            <Button
              className="cursor-pointer"
              onClick={handleCreateTask}
              disabled={isCreating}
            >
              {isCreating ? "Creating..." : "Create Task"}
            </Button>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mb-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 bg-[#212121] border border-[#404040] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full p-2 bg-[#212121] border border-[#404040] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>

        {filteredAndSortedTasks.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-400">No tasks found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-[#171717]">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                    Title
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                    Due Date
                  </th>
                  {!orgId && (
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                      Organization
                    </th>
                  )}
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                    Assignee
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredAndSortedTasks.map((task) => (
                  <tr
                    key={task.id}
                    className="hover:bg-[#1f1f1f] transition-colors"
                  >
                    <td className="px-4 py-3 text-sm text-white">
                      {task.title}
                    </td>
                    <td className="px-4 py-3">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(
                          task.status
                        )}`}
                      >
                        {task.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-white">
                      {new Date(task.dueAt).toLocaleDateString()}
                    </td>
                    {!orgId && (
                      <td className="px-4 py-3 text-sm text-white">
                        {task.organization?.name}
                      </td>
                    )}
                    <td className="px-4 py-3 text-sm text-white">
                      {task.assignee?.fullName || "Unassigned"}
                    </td>
                    <td className="px-4 py-3 text-sm space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedTask(task.id)}
                        disabled={isUpdating || isUpdatingStatus || isDeleting}
                      >
                        View
                      </Button>
                      {canEditTask(task) && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditTask(task)}
                          disabled={
                            isUpdating || isUpdatingStatus || isDeleting
                          }
                        >
                          Edit
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Task Form Modal */}
      {showTaskForm && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-[#171717] border border-[#333333] rounded-lg p-6 w-full max-w-2xl">
            <TaskForm
              orgId={orgId}
              initialData={transformTaskForForm(editTask)}
              onSubmit={handleTaskFormSubmit}
              onCancel={handleTaskFormCancel}
            />
          </div>
        </div>
      )}

      {/* Task Details Modal */}
      {selectedTask && (
        <TaskDetails
          taskId={selectedTask}
          onClose={() => setSelectedTask(null)}
          onUpdate={handleTaskFormSubmit}
          onDelete={() => {
            setSelectedTask(null);
          }}
          isOwner={isOwner}
        />
      )}
    </>
  );
}
