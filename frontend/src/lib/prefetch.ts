import { QueryClient, InfiniteData } from '@tanstack/react-query';

interface PrefetchOptions {
  staleTime?: number;
  cacheTime?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  nextPage: number | null;
  totalPages: number;
}

export const prefetchQuery = async <T>(
  queryClient: QueryClient,
  queryKey: readonly string[],
  queryFn: () => Promise<T>,
  options: PrefetchOptions = {}
) => {
  const defaultOptions = {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  };

  const mergedOptions = { ...defaultOptions, ...options };

  await queryClient.prefetchQuery({
    queryKey,
    queryFn,
    staleTime: mergedOptions.staleTime,
    gcTime: mergedOptions.cacheTime,
  });
};

export const prefetchInfiniteQuery = async <T>(
  queryClient: QueryClient,
  queryKey: readonly (string | Record<string, unknown>)[],
  queryFn: (page: number) => Promise<PaginatedResponse<T>>,
  options: PrefetchOptions = {}
) => {
  const defaultOptions = {
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  };

  const mergedOptions = { ...defaultOptions, ...options };

  await queryClient.prefetchInfiniteQuery<PaginatedResponse<T>, Error, number>({
    queryKey,
    queryFn: ({ pageParam = 1 }) => queryFn(Number(pageParam)),
    initialPageParam: 1,
    staleTime: mergedOptions.staleTime,
    gcTime: mergedOptions.cacheTime,
  });
}; 