{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "noEmit": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "react-jsx",

    /* Type Checking */
    "strict": false,  // Disable strict type checking temporarily
    "noUnusedLocals": false,  // Disable unused locals check
    "noUnusedParameters": false,  // Disable unused parameters check
    "noFallthroughCasesInSwitch": false,
    "allowJs": true,  // Allow JavaScript files
    "checkJs": false,  // Don't type check JavaScript files
    "noImplicitAny": false,  // Allow implicit any
    "suppressImplicitAnyIndexErrors": true,

    /* Paths */
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
