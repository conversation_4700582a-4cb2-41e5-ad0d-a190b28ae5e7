# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
bower_components
jspm_packages/

# Build output
dist/
dist-ssr/
.next
out
.nuxt
.vuepress/dist
.docusaurus
.serverless/

# TypeScript cache
*.tsbuildinfo

# Optional caches
.cache
.parcel-cache
.nyc_output
.grunt
.fusebox/
.dynamodb/
.pnpm-debug.log*
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Coverage directories
coverage/
*.lcov

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Finder (MacOS) folder config
.DS_Store

# IntelliJ based IDEs
.idea/

# Node.js runtime files
pids
*.pid
*.seed
*.pid.lock
.node_repl_history

# Compiled binary addons
build/Release/

# Output of 'npm pack'
*.tgz

# Optional npm cache directory
.npm

# Optional eslint and stylelint cache
.eslintcache
.stylelintcache

# Yarn v2 files
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Snowpack dependency directory
web_modules/

# DynamoDB Local files
.dynamodb/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Next.js and Gatsby files (adjust based on your project)
# Uncomment 'public' if using Gatsby and not Next.js
# public

